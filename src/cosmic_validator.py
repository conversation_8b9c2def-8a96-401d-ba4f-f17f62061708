#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC校验器

对main.py生成的cosmic功能拆解excel文件进行校验
"""

import pandas as pd
import json
import os
import time
import threading
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from collections import defaultdict
from typing import Dict, List, Tuple
from csv_2_xls import process_csv_to_excel
import sys
# 添加src目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import config

import llm_util
from config import ( CHECK_EXCLUDED_FIELDS,
    CHECK_OUTPUT_DIR, CHECK_RESULT_FILE,
    CHECK_PROMPT_FILE, CHECK_BATCH_COUNT, THREAD_COUNT, MAX_THREAD_COUNT
)


class CosmicValidator:
    """COSMIC校验器类 - 分离式校验"""

    def __init__(self):
        """初始化校验器"""
        self.llm_prompt = self._load_llm_prompt()
        self.excluded_fields = CHECK_EXCLUDED_FIELDS or []
        self.batch_count = CHECK_BATCH_COUNT or 50
        self.output_dir = CHECK_OUTPUT_DIR or "debug"

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        print(f"COSMIC校验器初始化完成")
        print(f"- 批次大小: {self.batch_count}")
        print(f"- 输出目录: {self.output_dir}")
        print(f"- 排除字段: {self.excluded_fields}")

    def _load_llm_prompt(self) -> str:
        """加载LLM校验提示词"""
        try:
            with open(CHECK_PROMPT_FILE, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"加载提示词失败: {e}!!!")
            raise e
    
    # ==================== Python程序检查方法 ====================

    def check_duplicate_data_attributes(self, df: pd.DataFrame) -> List[Dict]:
        """检查1: 不同行的数据属性必须不能相同"""
        issues = []
        data_attr_col = '数据属性'

        if data_attr_col not in df.columns:
            return issues

        # 统计数据属性出现次数
        attr_counts = df[data_attr_col].value_counts()
        duplicates = attr_counts[attr_counts > 1]

        for attr, count in duplicates.items():
            if pd.notna(attr) and str(attr).strip():
                duplicate_rows = df[df[data_attr_col] == attr].index.tolist()
                issues.append({
                    "issue_type": "数据属性重复",
                    "description": f"数据属性 '{attr}' 在 {count} 行中重复出现",
                    "affected_rows": duplicate_rows,
                    "severity": "高",
                    "value": attr
                })

        return issues

    def check_duplicate_function_processes(self, df: pd.DataFrame) -> List[Dict]:
        """检查2: 不同行的功能过程、子过程不能完全相同"""
        issues = []
        func_process_col = '功能过程'
        subprocess_col = '子过程描述'

        if func_process_col not in df.columns or subprocess_col not in df.columns:
            return issues

        # 创建功能过程+子过程的组合键
        df_temp = df.copy()
        df_temp['process_key'] = df_temp[func_process_col].astype(str) + "||" + df_temp[subprocess_col].astype(str)

        # 统计组合键出现次数
        process_counts = df_temp['process_key'].value_counts()
        duplicates = process_counts[process_counts > 1]

        for process_key, count in duplicates.items():
            if "||" in process_key:
                func_process, subprocess = process_key.split("||", 1)
                if func_process.strip() and subprocess.strip():
                    duplicate_rows = df_temp[df_temp['process_key'] == process_key].index.tolist()
                    issues.append({
                        "issue_type": "功能过程重复",
                        "description": f"功能过程 '{func_process}' + 子过程 '{subprocess}' 在 {count} 行中重复出现",
                        "affected_rows": duplicate_rows,
                        "severity": "高",
                        "function_process": func_process,
                        "subprocess": subprocess
                    })

        return issues

    def check_data_movement_sequence(self, df: pd.DataFrame) -> List[Dict]:
        """检查3: 每个功能过程的数据移动类型的第一步必须是E，最后一步必须是W或者X，不能有连续的E"""
        issues = []
        func_process_col = '功能过程'
        movement_col = '数据移动类型'

        if func_process_col not in df.columns or movement_col not in df.columns:
            return issues

        # 按功能过程分组
        grouped = df.groupby(func_process_col)

        for func_process, group in grouped:
            if pd.isna(func_process) or not str(func_process).strip():
                continue

            movements = group[movement_col].dropna().astype(str).str.strip().tolist()
            if not movements:
                continue

            group_rows = group.index.tolist()

            # 检查第一步是否为E
            if movements[0] != 'E':
                issues.append({
                    "issue_type": "数据移动序列错误",
                    "description": f"功能过程 '{func_process}' 的第一步不是E，而是 '{movements[0]}'",
                    "affected_rows": [group_rows[0]],
                    "severity": "高",
                    "function_process": func_process,
                    "expected": "E",
                    "actual": movements[0]
                })

            # 检查最后一步是否为W或X
            if len(movements) > 1 and movements[-1] not in ['W', 'X']:
                issues.append({
                    "issue_type": "数据移动序列错误",
                    "description": f"功能过程 '{func_process}' 的最后一步不是W或X，而是 '{movements[-1]}'",
                    "affected_rows": [group_rows[-1]],
                    "severity": "高",
                    "function_process": func_process,
                    "expected": "W或X",
                    "actual": movements[-1]
                })

            # 检查是否有连续的E
            for i in range(len(movements) - 1):
                if movements[i] == 'E' and movements[i + 1] == 'E':
                    issues.append({
                        "issue_type": "数据移动序列错误",
                        "description": f"功能过程 '{func_process}' 存在连续的E",
                        "affected_rows": [group_rows[i], group_rows[i + 1]],
                        "severity": "中",
                        "function_process": func_process,
                        "position": f"第{i+1}和第{i+2}步"
                    })

        return issues

    def check_and_fix_data_movement_sequence(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """检查并修复数据移动序列问题 - 通过移动整行数据"""
        issues = []
        func_process_col = '功能过程'
        movement_col = '数据移动类型'

        if func_process_col not in df.columns or movement_col not in df.columns:
            return df, issues

        # 创建DataFrame副本用于修复
        fixed_df = df.copy()

        # 按功能过程分组
        grouped = fixed_df.groupby(func_process_col, sort=False)

        # 存储所有需要重新排列的行
        rows_to_reorder = []
        other_rows = []

        for func_process, group in grouped:
            if pd.isna(func_process) or not str(func_process).strip():
                other_rows.extend(group.index.tolist())
                continue

            group_indices = group.index.tolist()
            movements = group[movement_col].fillna('').astype(str).str.strip().tolist()

            if not movements or all(not m for m in movements):
                other_rows.extend(group_indices)
                continue

            original_movements = movements.copy()
            reordered_indices = self._fix_movement_sequence_by_reordering(group_indices, movements, fixed_df)

            if reordered_indices != group_indices:
                # 记录修复问题
                issue = {
                    "issue_type": "数据移动序列修复",
                    "description": f"功能过程 '{func_process}' 的数据移动序列已通过重新排序修复",
                    "affected_rows": group_indices,
                    "severity": "中",
                    "function_process": func_process,
                    "original_sequence": " -> ".join(original_movements)
                }
                issues.append(issue)

            rows_to_reorder.extend(reordered_indices)

        # 重新构建DataFrame，保持修复后的行顺序
        if rows_to_reorder:
            all_ordered_indices = rows_to_reorder + other_rows
            fixed_df = fixed_df.loc[all_ordered_indices].reset_index(drop=True)

        return fixed_df, issues

    def _fix_movement_sequence_by_reordering(self, indices: List[int], movements: List[str], df: pd.DataFrame) -> List[int]:
        """通过重新排序整行数据来修复数据移动序列"""
        if not movements or not indices:
            return indices

        # 创建索引和移动类型的映射
        index_movement_pairs = list(zip(indices, movements))
        reordered_pairs = index_movement_pairs.copy()

        # 1. 如果第一个不是E，找到第一个E并移到开头
        if movements[0] != 'E':
            e_pair_index = -1
            for i, (idx, movement) in enumerate(reordered_pairs):
                if movement == 'E':
                    e_pair_index = i
                    break

            if e_pair_index > 0:
                # 将包含E的整行移到开头
                e_pair = reordered_pairs.pop(e_pair_index)
                reordered_pairs.insert(0, e_pair)

        # 2. 处理连续的E，将后续的E改为R（修改DataFrame中的值）
        for i in range(1, len(reordered_pairs)):
            if reordered_pairs[i][1] == 'E' and reordered_pairs[i-1][1] == 'E':
                # 修改DataFrame中的数据移动类型
                idx = reordered_pairs[i][0]
                df.loc[idx, '数据移动类型'] = 'R'
                # 更新pair中的移动类型
                reordered_pairs[i] = (idx, 'R')

        # 3. 如果最后一个是R，找到W并将R移到W之前
        current_movements = [pair[1] for pair in reordered_pairs]
        if len(current_movements) > 1 and current_movements[-1] == 'R':
            w_pair_index = -1
            for i in range(len(reordered_pairs) - 1, -1, -1):
                if reordered_pairs[i][1] == 'W':
                    w_pair_index = i
                    break

            if w_pair_index >= 0 and w_pair_index < len(reordered_pairs) - 1:
                # 将包含R的整行移到W之前
                r_pair = reordered_pairs.pop()
                reordered_pairs.insert(w_pair_index, r_pair)

        # 4. 如果过程中有X并且不是最后一个，则X移到最后
        current_movements = [pair[1] for pair in reordered_pairs]
        if len(current_movements) > 1 and current_movements[-1] != 'X':
            x_pair_index = -1
            for i in range(len(reordered_pairs) - 1, -1, -1):
                if reordered_pairs[i][1] == 'X':
                    x_pair_index = i
                    break

            if x_pair_index >= 0 and x_pair_index < len(reordered_pairs) - 1:
                # 将包含X的整行移到最后
                x_pair = reordered_pairs.pop(x_pair_index)
                reordered_pairs.append(x_pair)

        # 返回重新排序后的索引列表
        return [pair[0] for pair in reordered_pairs]

    def fix_data_move(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
        """运行Python程序检查和修复"""
        print("开始Python程序检查和修复...")

        all_issues = []

        # 检查1: 数据属性重复
        print("  检查1: 数据属性重复...")
        attr_issues = self.check_duplicate_data_attributes(df)
        all_issues.extend(attr_issues)
        print(f"    发现 {len(attr_issues)} 个数据属性重复问题")

        # 检查2: 功能过程重复
        print("  检查2: 功能过程重复...")
        process_issues = self.check_duplicate_function_processes(df)
        all_issues.extend(process_issues)
        print(f"    发现 {len(process_issues)} 个功能过程重复问题")

        # 检查3: 数据移动序列检查和修复
        print("  检查3: 数据移动序列检查和修复...")
        fixed_df, movement_issues = self.check_and_fix_data_movement_sequence(df)
        all_issues.extend(movement_issues)
        print(f"    发现并修复 {len(movement_issues)} 个数据移动序列问题")

        # 统计问题类型
        issue_types = {}
        for issue in all_issues:
            issue_type = issue.get('issue_type', '未知')
            issue_types[issue_type] = issue_types.get(issue_type, 0) + 1

        result = {
            "total_issues": len(all_issues),
            "issue_types": issue_types,
            "issues": all_issues,
            "summary": f"Python程序检查和修复完成，共发现 {len(all_issues)} 个问题"
        }

        print(f"Python程序检查和修复完成，共发现 {len(all_issues)} 个问题")
        return fixed_df, result

    # ==================== 文件处理方法 ====================

    def parse_excel_file(self, file_path: str) -> pd.DataFrame:
        """解析Excel或CSV文件"""
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path)            
            
            # 向后填充一级/二级模块列
            level_1_name, level_2_name, level_3_name, user,event,process_name = "一级功能模块", "二级功能模块","三级功能模块","功能用户","触发事件","功能过程"
            df[[level_1_name, level_2_name, level_3_name, user,event,process_name]] = df[[level_1_name, level_2_name, level_3_name, user,event,process_name]].ffill()
            
            print(f"成功解析文件: {file_path}")
            print(f"数据行数: {len(df)}")
            print(f"列数: {len(df.columns)}")
            
            return df
        except Exception as e:
            print(f"解析文件失败: {e}")
            return None
    
    def group_by_level2_module(self, df: pd.DataFrame) -> Dict[str, List[Dict]]:
        """按二级模块分组数据"""
        grouped_data = defaultdict(list)
        
        for _, row in df.iterrows():
            # 跳过空行
            if pd.isna(row.get('三级功能模块')) or str(row.get('三级功能模块')).strip() == '':
                continue
            
            level2_module = str(row.get('二级功能模块', ''))
            
            # 转换为字典，排除指定字段
            row_dict = {}
            for col, value in row.items():
                if col not in self.excluded_fields:
                    row_dict[col] = value if not pd.isna(value) else ""
            
            grouped_data[level2_module].append(row_dict)
        
        print(f"按二级模块分组完成，共 {len(grouped_data)} 个模块")
        for module, data in grouped_data.items():
            print(f"  {module}: {len(data)} 条记录")
        
        return dict(grouped_data)

    def run_llm_checks_and_fixes(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """运行LLM检查和修复"""
        print("开始LLM检查和修复...")

        # 按二级模块分组进行LLM处理
        grouped_data = self.group_by_level2_module(df)
        if not grouped_data:
            print("没有有效数据进行LLM处理")
            return df, []

        # 保存原始二级模块顺序
        original_module_order = list(grouped_data.keys())
        print(f"原始二级模块顺序: {original_module_order}")

        # 确定线程数
        thread_count = self.get_optimal_thread_count(len(grouped_data))
        print(f"使用 {thread_count} 个线程进行LLM处理")

        # 多线程LLM处理
        start_time = time.time()
        all_results = {}

        with ThreadPoolExecutor(max_workers=thread_count, thread_name_prefix="CosmicLLM") as executor:
            # 提交任务
            future_to_module = {}
            for module_name, module_data in grouped_data.items():
                future = executor.submit(self.process_module_with_llm, module_name, module_data)
                future_to_module[future] = module_name

            # 收集结果
            for future in as_completed(future_to_module):
                module_name = future_to_module[future]
                try:
                    result_module_name, result = future.result()
                    all_results[result_module_name] = result
                except Exception as exc:
                    print(f"模块 {module_name} LLM处理失败: {exc}")
                    all_results[module_name] = {"error": str(exc), "original_data": grouped_data[module_name]}

        processing_time = time.time() - start_time
        print(f"LLM处理完成，耗时: {processing_time:.2f}秒")

        # 按原始顺序合并修复后的数据
        fixed_df, llm_issues = self._merge_llm_results_ordered(all_results, df, original_module_order)

        return fixed_df, llm_issues

    def process_module_with_llm(self, level2_name: str, module_data: List[Dict]) -> Tuple[str, Dict]:
        """使用LLM处理单个二级模块，以三级模块为单位组织数据"""
        # 按三级模块分组数据
        level3_groups = self._group_by_level3_module(module_data)
        print(f"[线程{threading.current_thread().name}] 开始处理二级模块-[{level2_name}], 包含 {len(level3_groups)} 个三级模块")

        # 处理每个三级模块组
        all_fixed_data = []
        all_issues = []

        for level3_module, level3_data in level3_groups.items():
            if config.TEST_LEVEL3_NAME != "" and level3_module != config.TEST_LEVEL3_NAME:
                continue
            print(f"[线程{threading.current_thread().name}] 处理三级模块-[{level3_module}], {len(level3_data)} 个子过程")

            # 将三级模块数据组织为与提示词输出相同的结构
            structured_input = self._organize_level3_data_for_llm(level3_module, level3_data)

            # 调用LLM处理
            batch_id = f"{level2_name}_{level3_module}_llm"
            #print(f"LLM输入：{structured_input}")
            result = self._call_llm_for_level3_validation(structured_input, batch_id)
            #print(f"LLM输出：{result}")

            if "error" in result:
                print(f"三级模块 {level3_module} LLM处理失败: {result['error']}")
                # 如果LLM处理失败，保留原始数据
                all_fixed_data.extend(level3_data)
            else:
                # 处理LLM返回的三级模块结构结果                
                fixed_data, issues = self._process_level3_llm_result(result, level3_data)

                all_fixed_data.extend(fixed_data)
                all_issues.extend(issues)

            print(f"三级模块 {level3_module} LLM处理完成: 处理 {len(fixed_data)} 条记录，发现 {len(issues)} 个问题")

        # 汇总结果
        module_result = {
            "module_name": level2_name,
            "original_count": len(module_data),
            "fixed_count": len(all_fixed_data),
            "fixed_data": all_fixed_data,
            "issues_found": all_issues
        }

        print(f"[线程{threading.current_thread().name}] 完成LLM处理模块 {level2_name}: 原始{len(module_data)}条，修复{len(all_fixed_data)}条")

        return level2_name, module_result

    def _group_by_level3_module(self, module_data: List[Dict]) -> Dict[str, List[Dict]]:
        """按三级模块分组数据"""
        level3_groups = defaultdict(list)

        for record in module_data:
            level3_module = record.get('三级功能模块', '')
            if level3_module:
                level3_groups[level3_module].append(record)

        return dict(level3_groups)

    def _organize_level3_data_for_llm(self, level3_module: str, level3_data: List[Dict]) -> List[Dict]:
        """将三级模块数据组织为与提示词输出相同的结构"""
        # 按功能过程分组
        process_groups = defaultdict(list)

        for record in level3_data:
            function_process = record.get('功能过程', '')
            process_groups[function_process].append(record)

        # 构建结构化数据
        processes = []
        index = 1
        for function_process, records in process_groups.items():
            # 获取第一个记录的基本信息
            first_record = records[0]

            # 构建子过程列表
            subprocesses = []
            for record in records:
                subprocess_info = {
                    "编号": index,
                    "子过程描述": record.get('子过程描述', ''),
                    "数据移动类型": record.get('数据移动类型', ''),
                    "数据组": record.get('数据组', ''),
                    "数据属性": record.get('数据属性', '')
                }
                index += 1
                subprocesses.append(subprocess_info)

            # 构建功能过程信息
            process_info = {
                "功能用户": first_record.get('功能用户', ''),
                "触发事件": first_record.get('触发事件', ''),
                "功能过程": function_process,
                "子过程": subprocesses
            }
            processes.append(process_info)

        # 构建最终结构
        structured_data = [{level3_module: processes}]
        return structured_data

    def _call_llm_for_level3_validation(self, structured_input: List[Dict], batch_id: str) -> Dict:
        """调用LLM进行三级模块校验"""
        # 构建用户输入
        user_input = f"请检查并修复COSMIC数据中的问题。\n\n"
        user_input += "数据（已按三级模块组织）：\n"
        user_input += json.dumps(structured_input, ensure_ascii=False, indent=2)

        try:
            # 调用LLM
            result = llm_util.call_LLM(self.llm_prompt, user_input + " /nothink")

            if result:
                # 解析JSON结果
                parsed_result = llm_util.extract_json_from_content(result)
                if parsed_result:
                    return parsed_result
                else:
                    print(f"{batch_id} JSON解析失败")
                    return {"error": "JSON解析失败", "raw_content": result}
            else:
                print(f"{batch_id} LLM调用失败")
                return {"error": "LLM调用失败"}

        except Exception as e:
            print(f"{batch_id} 处理异常: {e}")
            return {"error": str(e)}

    def _process_level3_llm_result(self, llm_result: List[Dict], original_data: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """处理LLM返回的三级模块结构结果"""
        fixed_data = []
        issues_found = []

        # 创建原始数据的映射，用于补充模块信息
        # original_mapping = {}
        # for record in original_data:
        #     key = f"{record.get('三级功能模块', '')}_{record.get('功能过程', '')}_{record.get('子过程描述', '')}"
        #     original_mapping[key] = record

        # 处理LLM结果
        index = 1
        for org_subprocess in original_data:
            # 构建扁平记录
            record = {}
            record["一级功能模块"] = org_subprocess["一级功能模块"]
            record["二级功能模块"] = org_subprocess["二级功能模块"]
            record["三级功能模块"] = org_subprocess["三级功能模块"]
            record["功能用户"] = org_subprocess["功能用户"]
            record["触发事件"] = org_subprocess["触发事件"]
            record["功能过程"] = org_subprocess["功能过程"]
            
            # 从subprocesses中查询index属性相同的子过程
            llm_subprocess = [s for s in llm_result if s.get("编号", 0) == f"{index}"]
            if llm_subprocess:
                subprocess = llm_subprocess[0]
            else:
                subprocess = org_subprocess
            index += 1
            
            # 更新字段值
            record["子过程描述"] = subprocess.get('子过程描述', '')
            record["数据移动类型"] = subprocess.get('数据移动类型', '')
            record["数据组"] = subprocess.get('数据组', '')
            record["数据属性"] = subprocess.get('数据属性', '')
            record["CFP"] = 1
            fixed_data.append(record)

            # 收集修复说明
            fix_note = subprocess.get('修复说明', '')
            if fix_note and fix_note.strip():
                issue = {
                    "issue_type": "LLM修复",
                    "description": fix_note,
                    "fix_applied": fix_note,
                    "subprocess": record["子过程描述"]
                }
                issues_found.append(issue)
        
        return fixed_data, issues_found

    def _merge_llm_results(self, all_results: Dict, original_df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """合并LLM处理结果"""
        all_fixed_data = []
        all_issues = []

        for module_name, result in all_results.items():
            if "error" in result:
                print(f"模块 {module_name} 有错误，使用原始数据: {result['error']}")
                # 使用原始数据
                if "original_data" in result:
                    all_fixed_data.extend(result["original_data"])
                continue

            all_fixed_data.extend(result.get('fixed_data', []))
            all_issues.extend(result.get('issues_found', []))

        # 如果没有修复数据，返回原始数据
        if not all_fixed_data:
            print("没有LLM修复数据，返回原始数据")
            return original_df, all_issues

        # 创建修复后的DataFrame
        try:
            fixed_df = pd.DataFrame(all_fixed_data)
            print(f"LLM修复完成: 原始 {len(original_df)} 条 -> 修复后 {len(fixed_df)} 条")
            return fixed_df, all_issues
        except Exception as e:
            print(f"创建修复后DataFrame失败: {e}，返回原始数据")
            return original_df, all_issues

    def _merge_llm_results_ordered(self, all_results: Dict, original_df: pd.DataFrame, module_order: List[str]) -> Tuple[pd.DataFrame, List[Dict]]:
        """按照原始二级模块顺序合并LLM处理结果"""
        all_fixed_data = []
        all_issues = []

        # 按照原始顺序处理模块
        for module_name in module_order:
            if module_name not in all_results:
                print(f"警告: 模块 {module_name} 没有处理结果")
                continue

            result = all_results[module_name]
            if "error" in result:
                print(f"模块 {module_name} 有错误，使用原始数据: {result['error']}")
                # 使用原始数据
                if "original_data" in result:
                    all_fixed_data.extend(result["original_data"])
                continue

            all_fixed_data.extend(result.get('fixed_data', []))
            all_issues.extend(result.get('issues_found', []))

        # 如果没有修复数据，返回原始数据
        if not all_fixed_data:
            print("没有LLM修复数据，返回原始数据")
            return original_df, all_issues

        # 创建修复后的DataFrame
        try:
            fixed_df = pd.DataFrame(all_fixed_data)
            print(f"LLM修复完成（按原始顺序）: 原始 {len(original_df)} 条 -> 修复后 {len(fixed_df)} 条")
            return fixed_df, all_issues
        except Exception as e:
            print(f"创建修复后DataFrame失败: {e}，返回原始数据")
            return original_df, all_issues

    # ==================== 主要校验方法 ====================

    # ==================== 工具方法 ====================
    
    def get_optimal_thread_count(self, module_count: int) -> int:
        """获取最优线程数"""
        if THREAD_COUNT > 0:
            optimal_threads = min(THREAD_COUNT, module_count)
        else:
            cpu_count = os.cpu_count() or 4
            optimal_threads = min(cpu_count, module_count)
        
        optimal_threads = min(optimal_threads, MAX_THREAD_COUNT)
        return max(1, optimal_threads)

    def fix_data_group_consistency(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """检查并修复数据组属性一致性问题"""
        print("开始检查数据组属性一致性...")

        issues = []
        data_group_col = '数据组'
        data_attr_col = '数据属性'

        if data_group_col not in df.columns or data_attr_col not in df.columns:
            return df, issues

        # 创建DataFrame副本用于修复
        fixed_df = df.copy()

        # 按数据组分组，收集所有属性（保持顺序）
        group_attrs = {}
        for _, row in fixed_df.iterrows():
            data_group = str(row[data_group_col]).strip()
            data_attr = str(row[data_attr_col]).strip()

            if data_group and data_attr and data_group != 'nan' and data_attr != 'nan':
                if data_group not in group_attrs:
                    group_attrs[data_group] = []

                # 将属性值按"、"分割，保持顺序去重
                attr_parts = [part.strip() for part in data_attr.split('、') if part.strip()]
                for part in attr_parts:
                    if part not in group_attrs[data_group]:
                        group_attrs[data_group].append(part)

        # 检查并修复不一致的数据组
        for data_group, attrs in group_attrs.items():
            # 保持原始顺序合并属性
            unified_attr = "、".join(attrs)

            # 检查是否需要修复（获取该数据组的所有原始属性值）
            mask = fixed_df[data_group_col].astype(str).str.strip() == data_group
            original_attrs = fixed_df[mask][data_attr_col].unique()

            # 检查是否有不一致或重复的属性
            needs_fix = False
            for orig_attr in original_attrs:
                orig_attr_str = str(orig_attr).strip()
                if orig_attr_str != unified_attr:
                    # 检查是否有重复属性
                    orig_parts = [part.strip() for part in orig_attr_str.split('、') if part.strip()]
                    if len(orig_parts) != len(set(orig_parts)) or orig_attr_str != unified_attr:
                        needs_fix = True
                        break

            if needs_fix:
                # 统一替换为合并后的属性值
                affected_rows = fixed_df[mask].index.tolist()
                fixed_df.loc[mask, data_attr_col] = unified_attr

                issues.append({
                    "issue_type": "数据组属性一致性修复",
                    "description": f"数据组 '{data_group}' 的属性已去重并统一为: '{unified_attr}'",
                    "affected_rows": affected_rows,
                    "severity": "中",
                    "data_group": data_group,
                    "original_attrs": [str(attr) for attr in original_attrs],
                    "unified_attr": unified_attr
                })

                print(f"  修复数据组 '{data_group}': {[str(attr) for attr in original_attrs]} -> '{unified_attr}'")

        print(f"数据组属性一致性检查完成，发现并修复 {len(issues)} 个问题")
        return fixed_df, issues

    def fix_english_content(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """检查并翻译英文内容"""
        print("开始检查并翻译英文内容...")

        issues = []
        data_group_col = '数据组'
        data_attr_col = '数据属性'

        if data_group_col not in df.columns or data_attr_col not in df.columns:
            return df, issues

        # 创建DataFrame副本用于修复
        fixed_df = df.copy()

        # 检查需要翻译的内容
        english_items = []

        for idx, row in fixed_df.iterrows():
            data_group = str(row[data_group_col]).strip()
            data_attr = str(row[data_attr_col]).strip()

            # 检查数据组是否包含纯英文
            if data_group and data_group != 'nan' and self._is_pure_english(data_group):
                english_items.append({
                    'row': idx,
                    '数据组': data_group,
                    '数据属性': data_attr
                })
            # 检查数据属性是否包含纯英文
            elif data_attr and data_attr != 'nan' and self._is_pure_english(data_attr):
                english_items.append({
                    'row': idx,
                    '数据组': data_group,
                    '数据属性': data_attr
                })

        if english_items:
            print(f"发现 {len(english_items)} 个需要翻译的英文内容")

            # 批量翻译
            translated_items = self._translate_english_batch(english_items)

            # 应用翻译结果
            for item in translated_items:
                if 'translated_text' in item:
                    row_idx = item['row_idx']
                    column = item['column']
                    original_text = item['original_text']
                    translated_text = item['translated_text']

                    fixed_df.loc[row_idx, column] = translated_text

                    issues.append({
                        "issue_type": "英文翻译修复",
                        "description": f"{item['type']} '{original_text}' 已翻译为 '{translated_text}'",
                        "affected_rows": [row_idx],
                        "severity": "中",
                        "original_text": original_text,
                        "translated_text": translated_text,
                        "content_type": item['type']
                    })

        print(f"英文内容检查和翻译完成，处理了 {len(issues)} 个项目")
        return fixed_df, issues

    def _is_pure_english(self, text: str) -> bool:
        """检查文本是否为纯英文（不包含中文字符）"""
        # 检查是否包含中文字符
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        has_chinese = bool(chinese_pattern.search(text))

        # 检查是否包含英文字母
        english_pattern = re.compile(r'[a-zA-Z]')
        has_english = bool(english_pattern.search(text))
        # 检查英文是否都是“ID”这个词
        if has_english and not has_chinese:
            # 提取所有英文字母
            english_chars = re.findall(r'[a-zA-Z]+', text)
            # 合并所有英文单词并转为大写
            english_text = ''.join(english_chars).upper()
            # 如果英文部分不完全是"ID"，则认为是纯英文需要翻译
            if english_text != 'ID':
                return True
        

        # 如果有英文但没有中文，且不是纯数字或符号，则认为是纯英文
        # if has_english and not has_chinese:
        #     # 排除纯数字、纯符号的情况
        #     alphanumeric_pattern = re.compile(r'[a-zA-Z0-9]')
        #     return bool(alphanumeric_pattern.search(text))

        return False

    def _translate_english_batch(self, english_items: List[Dict]) -> List[Dict]:
        """批量翻译英文内容"""
        if not english_items:
            return english_items

        # 构建翻译提示词
        translate_prompt = """你是一个专业的中英文翻译专家，将以下数据组和数据属性的英文翻译为中文，数据属性值翻译时可参考数据组的名称，如：数据组=USER_CERT,数据属性=ID、USER_ID、CERT、SERIAL、CERT_NAME, 则翻译后为：数据组=用户证书,数据属性=用户证书ID、用户ID、用户证书、序列号、C证书名称。

请按照输入的顺序以JSON格式返回翻译结果：
```json
[
    {"数据组": "{原文或翻译结果}", "数据属性": "{原文或翻译结果}"},
    {"数据组": "{原文或翻译结果}", "数据属性": "{原文或翻译结果}"}
]
```"""

        # 准备翻译内容
        translate_items = []
        for item in english_items:
            translate_items.append({
                "original": item['original_text'],
                "type": item['type']
            })

        user_input = f"需要翻译的内容：\n{json.dumps(translate_items, ensure_ascii=False, indent=2)}"

        try:
            # 调用LLM进行翻译
            result = llm_util.call_LLM(translate_prompt, user_input + " /nothink")

            if result:
                # 解析JSON结果
                parsed_result = llm_util.extract_json_from_content(result)
                if parsed_result and isinstance(parsed_result, list):
                    # 将翻译结果映射回原始项目
                    translation_map = {}
                    for item in parsed_result:
                        if 'original' in item and 'translated' in item:
                            translation_map[item['original']] = item['translated']

                    # 更新英文项目的翻译结果
                    for item in english_items:
                        original_text = item['original_text']
                        if original_text in translation_map:
                            item['translated_text'] = translation_map[original_text]
                        else:
                            # 如果没有找到翻译，保持原文
                            item['translated_text'] = original_text
                            print(f"警告: 未找到 '{original_text}' 的翻译结果")

                    return english_items
                else:
                    print("翻译结果JSON解析失败")
            else:
                print("LLM翻译调用失败")

        except Exception as e:
            print(f"批量翻译失败: {e}")

        # 如果翻译失败，保持原文
        for item in english_items:
            item['translated_text'] = item['original_text']

        return english_items

    def validate_file(self, input_file: str, output_file: str = None) -> Dict:
        """分离式校验整个文件"""
        print(f"开始分离式校验文件: {input_file}")
        start_time = time.time()

        # 解析文件
        df = self.parse_excel_file(input_file)
        if df is None:
            return {"error": "文件解析失败"}

        print(f"原始数据: {len(df)} 行")

        # ==================== A. Python程序检查和修复 ====================
        fixed_df, python_check_result = self.fix_data_move(df)

        # ==================== D. 英文内容检查和翻译 ====================
        translation_issues = []
        final_df, translation_issues = self.fix_english_content(fixed_df)

        # ==================== B. LLM检查和修复 ====================
        #final_df = fixed_df
        llm_issues = []
        #inal_df, llm_issues = self.run_llm_checks_and_fixes(final_df)

        # ==================== C. 数据组属性一致性检查和修复 ====================
        consistency_issues = []
        #final_df, consistency_issues = self.fix_data_group_consistency(final_df)

        # ==================== E. 合并结果并输出 ====================
        processing_time = time.time() - start_time

        # 汇总所有结果
        all_post_llm_issues = consistency_issues + translation_issues
        final_result = {
            "input_file": input_file,
            "processing_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "duration_seconds": processing_time,
            "original_records": len(df),
            "fixed_records": len(final_df),
            "python_checks": python_check_result,
            "llm_issues": llm_issues,
            "consistency_issues": consistency_issues,
            "translation_issues": translation_issues,
            "summary": {
                "python_issues_found": python_check_result["total_issues"],
                "llm_issues_found": len(llm_issues),
                "consistency_issues_found": len(consistency_issues),
                "translation_issues_found": len(translation_issues),
                "total_issues": python_check_result["total_issues"] + len(llm_issues) + len(all_post_llm_issues),
                "data_changed": len(final_df) != len(df)
            }
        }

        # 保存结果
        self._save_results(final_result, final_df, output_file)

        print(f"分离式校验完成，耗时: {processing_time:.2f}秒")
        print(f"Python检查发现 {python_check_result['total_issues']} 个问题")
        print(f"LLM修复发现 {len(llm_issues)} 个问题")
        print(f"数据组属性一致性检查发现 {len(consistency_issues)} 个问题")
        print(f"英文内容翻译处理了 {len(translation_issues)} 个项目")

        return final_result



    def _save_results(self, result: Dict, fixed_df: pd.DataFrame, output_file: str = None):
        """保存分离式校验结果"""
        # 保存完整结果
        result_file = os.path.join(self.output_dir, CHECK_RESULT_FILE)
        try:
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"校验结果已保存到: {result_file}")
        except Exception as e:
            print(f"保存校验结果失败: {e}")

        # 保存修复后的数据到Excel/CSV
        if output_file is None:
            timestamp = time.strftime("%m%d%H%M")
            output_file = os.path.join(self.output_dir, f"cosmic_validated_{timestamp}.xlsx")

        try:
            if len(fixed_df) > 0:
                if output_file.endswith('.csv'):
                    fixed_df.to_csv(output_file, index=False, encoding='utf-8-sig')
                else:
                    fixed_df.to_excel(output_file, index=False)

                print(f"修复后的数据已保存到: {output_file}")
            else:
                print("没有修复数据需要保存")
        except Exception as e:
            print(f"保存修复数据失败: {e}")

        # 生成校验报告
        self._generate_report(result)

    def _generate_report(self, result: Dict):
        """生成分离式校验报告"""
        report_file = os.path.join(self.output_dir, "cosmic_validation_report.txt")

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("COSMIC分离式校验报告\n")
                f.write("=" * 50 + "\n\n")

                # 基本信息
                f.write(f"输入文件: {result.get('input_file', 'N/A')}\n")
                f.write(f"处理时间: {result.get('processing_time', 'N/A')}\n")
                f.write(f"处理耗时: {result.get('duration_seconds', 0):.2f} 秒\n\n")

                # 汇总信息
                f.write("汇总信息:\n")
                f.write(f"  原始记录数: {result.get('original_records', 0)}\n")
                f.write(f"  修复记录数: {result.get('fixed_records', 0)}\n")

                summary = result.get('summary', {})
                f.write(f"  Python检查问题: {summary.get('python_issues_found', 0)}\n")
                f.write(f"  LLM修复问题: {summary.get('llm_issues_found', 0)}\n")
                f.write(f"  总问题数: {summary.get('total_issues', 0)}\n")
                f.write(f"  数据是否变更: {'是' if summary.get('data_changed', False) else '否'}\n\n")

                # Python检查详情
                python_checks = result.get('python_checks', {})
                f.write("Python程序检查详情:\n")
                f.write(f"  总问题数: {python_checks.get('total_issues', 0)}\n")

                issue_types = python_checks.get('issue_types', {})
                if issue_types:
                    f.write("  问题类型分布:\n")
                    for issue_type, count in issue_types.items():
                        f.write(f"    {issue_type}: {count} 个\n")

                # Python检查问题详情
                python_issues = python_checks.get('issues', [])
                if python_issues:
                    f.write("\n  Python检查发现的问题:\n")
                    for i, issue in enumerate(python_issues[:10]):  # 只显示前10个
                        f.write(f"    {i+1}. {issue.get('issue_type', '未知')}: {issue.get('description', 'N/A')}\n")
                        f.write(f"       影响行: {issue.get('affected_rows', [])}\n")
                    if len(python_issues) > 10:
                        f.write(f"    ... 还有 {len(python_issues) - 10} 个问题\n")

                # LLM修复详情
                llm_issues = result.get('llm_issues', [])
                f.write(f"\nLLM修复详情:\n")
                f.write(f"  修复问题数: {len(llm_issues)}\n")

                if llm_issues:
                    llm_issue_types = {}
                    for issue in llm_issues:
                        issue_type = issue.get('issue_type', '未知')
                        llm_issue_types[issue_type] = llm_issue_types.get(issue_type, 0) + 1

                    f.write("  LLM修复问题类型分布:\n")
                    for issue_type, count in llm_issue_types.items():
                        f.write(f"    {issue_type}: {count} 个\n")

            print(f"校验报告已保存到: {report_file}")
        except Exception as e:
            print(f"生成校验报告失败: {e}")


def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = "data/附件4：甘肃移动-软件功能-test2.xlsx"

    # 输出文件处理
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    if not output_file:
        # 生成默认输出文件名
        base_name = os.path.splitext(input_file)[0]
        output_file = base_name + f"_{config.MODEL_NAME}_修复.csv"

    print("COSMIC分离式校验器")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("=" * 50)

    # 创建校验器
    validator = CosmicValidator()

    # 执行分离式校验
    result = validator.validate_file(input_file, output_file)
    process_csv_to_excel(output_file)

    if "error" in result:
        print(f"校验失败: {result['error']}")
    else:
        print("\n" + "=" * 50)
        print("分离式校验完成！")
        print("=" * 50)

        summary = result.get('summary', {})
        print(f"Python检查问题: {summary.get('python_issues_found', 0)}")
        print(f"LLM修复问题: {summary.get('llm_issues_found', 0)}")
        print(f"总问题数: {summary.get('total_issues', 0)}")
        print(f"数据是否变更: {'是' if summary.get('data_changed', False) else '否'}")
        print(f"\n详细报告请查看: {validator.output_dir}/cosmic_validation_report.txt")


if __name__ == "__main__":
    main()
